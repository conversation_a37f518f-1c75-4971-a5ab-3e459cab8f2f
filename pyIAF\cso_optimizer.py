"""
Competitive Swarm Optimizer (CSO) for IAF-FBO
"""

import numpy as np
from .utils import normalize_data, onehot_conv


class CSO:
    """
    Competitive Swarm Optimizer for optimizing implicit acquisition function
    """
    
    def __init__(self, pop_size=100, max_iter=100, phi=0.1):
        """
        Initialize CSO optimizer
        
        Args:
            pop_size: Population size
            max_iter: Maximum iterations
            phi: Learning factor
        """
        self.pop_size = pop_size
        self.max_iter = max_iter
        self.phi = phi
        
    def optimize(self, classifier, bounds, dimension, initial_population=None):
        """
        Optimize using competitive swarm optimizer
        
        Args:
            classifier: Trained neural network classifier
            bounds: (lower_bound, upper_bound) for the search space
            dimension: Problem dimension
            initial_population: Initial population (optional)
        
        Returns:
            Best solution found
        """
        lower_bound, upper_bound = bounds
        
        # Initialize population
        if initial_population is not None:
            # Ensure population size is even
            if len(initial_population) % 2 == 1:
                # Add one random individual
                random_ind = np.random.uniform(lower_bound, upper_bound, (1, dimension))
                population = np.vstack([random_ind, initial_population])
            else:
                population = initial_population.copy()
            
            # Adjust population size
            current_size = len(population)
            if current_size < self.pop_size:
                # Add random individuals
                n_add = self.pop_size - current_size
                random_pop = np.random.uniform(lower_bound, upper_bound, (n_add, dimension))
                population = np.vstack([population, random_pop])
            elif current_size > self.pop_size:
                # Randomly select individuals
                indices = np.random.choice(current_size, self.pop_size, replace=False)
                population = population[indices]
        else:
            # Random initialization
            population = np.random.uniform(lower_bound, upper_bound, (self.pop_size, dimension))
        
        # Ensure even population size
        if len(population) % 2 == 1:
            random_ind = np.random.uniform(lower_bound, upper_bound, (1, dimension))
            population = np.vstack([population, random_ind])
        
        # Shuffle population
        indices = np.random.permutation(len(population))
        population = population[indices]
        
        # Initialize velocities
        velocities = np.zeros_like(population)
        
        # Main optimization loop
        for iteration in range(self.max_iter):
            # Random permutation for competition
            indices = np.random.permutation(len(population))
            half_size = len(population) // 2
            
            losers_idx = indices[:half_size]
            winners_idx = indices[half_size:]
            
            # Create pairwise comparison input for classifier
            losers = population[losers_idx]
            winners = population[winners_idx]
            
            # Prepare input for classifier: [loser, winner] pairs
            comparison_input = np.hstack([losers, winners])
            
            # Normalize input for classifier
            comparison_input_norm = normalize_data(comparison_input, bounds)
            
            # Predict using classifier
            try:
                # Assuming classifier is a PyTorch model or similar
                if hasattr(classifier, 'predict'):
                    predictions = classifier.predict(comparison_input_norm)
                else:
                    # For PyTorch models
                    import torch
                    with torch.no_grad():
                        classifier.eval()
                        tensor_input = torch.FloatTensor(comparison_input_norm)
                        predictions = classifier(tensor_input).numpy()
                
                # Convert predictions to labels
                predicted_labels = onehot_conv(predictions, mode=2)
                
                # Find pairs where loser should win (prediction = -1)
                swap_indices = np.where(predicted_labels == -1)[0]
                
                # Swap losers and winners for these pairs
                temp_losers = losers_idx[swap_indices].copy()
                losers_idx[swap_indices] = winners_idx[swap_indices]
                winners_idx[swap_indices] = temp_losers
                
            except Exception as e:
                print(f"Warning: Classifier prediction failed: {e}")
                # Continue with random competition if classifier fails
                pass
            
            # Update losers by learning from winners
            loser_pop = population[losers_idx]
            winner_pop = population[winners_idx]
            loser_vel = velocities[losers_idx]
            
            # Generate random factors
            r1 = np.random.rand(half_size, 1)
            r2 = np.random.rand(half_size, 1)
            r3 = np.random.rand(half_size, 1)
            
            # Calculate population mean
            pop_mean = np.mean(population, axis=0)
            
            # Update velocities
            new_loser_vel = (r1 * loser_vel + 
                           self.phi * r2 * (winner_pop - loser_pop) + 
                           r3 * (1 - 0) * (pop_mean - loser_pop))
            
            # Update positions
            new_loser_pop = loser_pop + new_loser_vel
            
            # Apply bounds
            new_loser_pop = np.clip(new_loser_pop, lower_bound, upper_bound)
            
            # Update population
            population[losers_idx] = new_loser_pop
            velocities[losers_idx] = new_loser_vel
        
        # Return random best solution (since we don't have explicit fitness)
        best_idx = np.random.randint(len(population))
        return population[best_idx]


def competitive_swarm_optimization(classifier, bounds, dimension, 
                                 initial_population=None, pop_size=100, 
                                 max_iter=100, phi=0.1):
    """
    Standalone function for competitive swarm optimization
    
    Args:
        classifier: Trained neural network classifier
        bounds: (lower_bound, upper_bound) for the search space
        dimension: Problem dimension
        initial_population: Initial population (optional)
        pop_size: Population size
        max_iter: Maximum iterations
        phi: Learning factor
    
    Returns:
        Best solution found
    """
    cso = CSO(pop_size=pop_size, max_iter=max_iter, phi=phi)
    return cso.optimize(classifier, bounds, dimension, initial_population)
